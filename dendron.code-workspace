{"folders": [{"path": "dependencies/github.com/jamesmiller2404/Binary_Bitch_Story"}, {"path": ".", "name": "Writing_Repo_for_VS-CODE_3"}], "settings": {"dendron.rootDir": ".", "files.autoSave": "onFocusChange", "pasteImage.path": "${currentFileDir}/assets/images", "pasteImage.prefix": "/", "markdown-preview-enhanced.enableWikiLinkSyntax": true, "markdown-preview-enhanced.wikiLinkFileExtension": ".md", "editor.snippetSuggestions": "inline", "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.suggest.showSnippets": true, "editor.tabCompletion": "on", "dendron.enableSelfContainedVaultWorkspace": true}, "extensions": {"recommendations": ["dendron.dendron", "dendron.dendron-paste-image", "dendron.dendron-markdown-shortcuts", "redhat.vscode-yaml"], "unwantedRecommendations": ["dendron.dendron-markdown-links", "dendron.dendron-markdown-notes", "dendron.dendron-markdown-preview-enhanced", "shd101wyy.markdown-preview-enhanced", "kortina.vscode-markdown-notes", "mushan.vscode-paste-image"]}}