#!/bin/bash

# Backup all writing projects to GitHub
echo "=== Backing up all writing projects ==="
echo

# Function to backup a project
backup_project() {
    local project_path=$1
    local project_name=$(basename "$project_path")
    
    echo "📚 Backing up: $project_name"
    cd "$project_path"
    
    # Check if there are changes to commit
    if [ -n "$(git status --porcelain)" ]; then
        echo "  📝 Found changes, committing..."
        git add .
        
        # Prompt for commit message or use default
        read -p "  💬 Commit message (or press Enter for default): " commit_msg
        if [ -z "$commit_msg" ]; then
            commit_msg="Writing session backup - $(date '+%Y-%m-%d %H:%M')"
        fi
        
        git commit -m "$commit_msg"
        echo "  ✅ Committed"
    else
        echo "  ✅ No changes to commit"
    fi
    
    # Push to remote
    echo "  ⬆️  Pushing to GitHub..."
    if git push origin main; then
        echo "  ✅ Successfully backed up to GitHub"
    else
        echo "  ❌ Failed to push to GitHub"
    fi
    
    cd - > /dev/null
    echo
}

# Backup main workspace
echo "📝 Main Workspace:"
if [ -n "$(git status --porcelain)" ]; then
    git add .
    read -p "💬 Main workspace commit message: " main_msg
    if [ -z "$main_msg" ]; then
        main_msg="Workspace update - $(date '+%Y-%m-%d %H:%M')"
    fi
    git commit -m "$main_msg"
    git push origin main
    echo "✅ Main workspace backed up"
else
    echo "✅ Main workspace clean"
fi
echo

# Backup each project
for project_dir in dependencies/github.com/jamesmiller2404/*/; do
    if [ -d "$project_dir" ]; then
        backup_project "$project_dir"
    fi
done

echo "🎉 All projects backed up!"
