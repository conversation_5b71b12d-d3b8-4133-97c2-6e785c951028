#!/bin/bash

# Check status of all writing projects
echo "=== Writing Projects Git Status ==="
echo

# Check main workspace
echo "📝 Main Workspace (Writing_Repo_for_VS-CODE_3):"
git status --porcelain
if [ $? -eq 0 ] && [ -z "$(git status --porcelain)" ]; then
    echo "✅ Clean"
else
    echo "📝 Has changes"
fi
echo

# Check each project in dependencies
for project_dir in dependencies/github.com/jamesmiller2404/*/; do
    if [ -d "$project_dir" ]; then
        project_name=$(basename "$project_dir")
        echo "📚 Project: $project_name"
        cd "$project_dir"
        
        # Check for uncommitted changes
        if [ -z "$(git status --porcelain)" ]; then
            echo "✅ Clean"
        else
            echo "📝 Has uncommitted changes:"
            git status --porcelain
        fi
        
        # Check if ahead of remote
        ahead=$(git rev-list --count @{u}..HEAD 2>/dev/null)
        if [ "$ahead" -gt 0 ] 2>/dev/null; then
            echo "⬆️  $ahead commits ahead of remote"
        fi
        
        cd - > /dev/null
        echo
    fi
done
