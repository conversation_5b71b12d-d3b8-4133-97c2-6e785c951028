---
id: root
title: Writing Repository
desc: Central hub for all writing projects
updated: 1721000000000
created: 1721000000000
---

# Writing Repository

Welcome to your central writing workspace! This is the main hub for managing all your writing projects.

## Structure

- **Main Vault**: This workspace serves as your central command center
- **Project Vaults**: Individual writing projects are managed as separate remote repositories in the `dependencies` folder

## Current Projects

- [[Binary_Bitch_Story|dendron://Binary_Bitch_Story/root]] - Your current writing project

## Getting Started

Use Dendron's lookup feature (`Ctrl+L`) to:
- Create new notes
- Navigate between projects
- Search across all your writing

## Project Management

Each writing project should be:
1. A separate Git repository
2. Added as a vault in the `dependencies` folder
3. Configured in `dendron.yml` with proper remote settings

This setup allows you to:
- Keep each project's version history separate
- Share individual projects easily
- Maintain a unified workspace for all your writing
